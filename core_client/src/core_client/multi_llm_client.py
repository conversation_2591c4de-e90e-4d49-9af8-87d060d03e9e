"""
Multi-LLM Client for MCP tool calling using Google ADK.

This module provides a client that can interact with multiple LLM providers
(OpenAI, Anthropic, and Google Gemini) simultaneously, using the MCP protocol
for tool calling. It supports:

- Running queries across multiple models in parallel
- Interactive chat mode with single or multiple models
- Consistent system prompt management across all models
- MCP tool calling for all supported providers
- Integration with Google ADK for unified handling of all providers
- Dynamic model discovery from available API keys
"""
from typing import List, Dict, Any, Optional, Callable

from .clients import ADKClient
from .utils import get_logger, configure_logging
from .utils.model_discovery import get_all_available_models

class MultiLLMClient:
    """
    Client for interacting with multiple LLM providers simultaneously using Google ADK.

    This client can send the same query to multiple LLM models from different
    providers (OpenAI, Anthropic, Google Gemini) and collect their responses.
    It supports MCP tool calling for all providers through Google ADK.
    """

    def __init__(self, models: Optional[List[str]] = None, system_prompt: Optional[str] = None):
        """
        Initialize the Multi-LLM client.

        Args:
            models: List of model IDs to use. If None, uses default models.
                   Format: "provider/model_id" (e.g., "openai/gpt-4o")
            system_prompt: Optional system prompt to guide the models' behavior
        """
        configure_logging()
        self.logger = get_logger(__name__)
        self.logger.info("Initializing MultiLLMClient")
        self.logger.debug(f"Models: {models}, System prompt length: {len(system_prompt) if system_prompt else 0}")

        # Create the ADK client
        self.client = ADKClient(models=models, system_prompt=system_prompt)
        self.logger.info("MultiLLMClient initialized successfully")

    async def connect_to_server(self, config_path: Optional[str] = None, server_names: Optional[List[str]] = None):
        """
        Connect to one or more MCP servers using configuration.

        Args:
            config_path: Path to the configuration file. If None, uses default config.json
            server_names: List of server names to connect to. If None, uses the default server

        Returns:
            List of MCP toolsets connected to the servers
        """
        self.logger.info(f"Connecting to MCP server(s): {server_names or 'default'}")
        self.logger.debug(f"Config path: {config_path}")

        try:
            result = await self.client.connect_to_server(config_path, server_names)
            self.logger.info("Successfully connected to MCP server(s)")
            return result
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server(s): {str(e)}")
            raise

    async def update_system_prompt(self, system_prompt: str):
        """
        Update the system prompt for all agents.

        Args:
            system_prompt: The new system prompt to use. If empty, uses the default system prompt.
        """
        self.logger.info("Updating system prompt for all agents")
        self.logger.debug(f"New system prompt length: {len(system_prompt) if system_prompt else 0}")

        try:
            await self.client.update_system_prompt(system_prompt)
            self.logger.info("System prompt updated successfully")
        except Exception as e:
            self.logger.error(f"Failed to update system prompt: {str(e)}")
            raise

    async def process_query(self, query: str) -> Dict[str, Dict[str, Any]]:
        """
        Process a query using all configured LLMs without streaming.

        Args:
            query: The user query to process

        Returns:
            Dict mapping model IDs to their responses
        """
        self.logger.info("Processing query (non-streaming)")
        self.logger.debug(f"Query length: {len(query)}")

        try:
            result = await self.client.process_query(query)
            self.logger.info(f"Query processed successfully for {len(result)} model(s)")
            return result
        except Exception as e:
            self.logger.error(f"Failed to process query: {str(e)}")
            raise

    async def stream_tokens(self, query: str, callbacks: Dict[str, Callable] = None, sequential: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Stream tokens from all configured LLMs with tool calling support.

        Args:
            query: The user query to process
            callbacks: Dict mapping model IDs to callback functions
            sequential: Whether to process models sequentially

        Returns:
            Dict mapping model IDs to their responses
        """
        self.logger.info(f"Streaming tokens (sequential: {sequential})")
        self.logger.debug(f"Query length: {len(query)}, Callbacks: {list(callbacks.keys()) if callbacks else 'None'}")

        try:
            result = await self.client.stream_tokens(query, callbacks, sequential)
            self.logger.info(f"Token streaming completed for {len(result)} model(s)")
            return result
        except Exception as e:
            self.logger.error(f"Failed to stream tokens: {str(e)}")
            raise

    async def list_available_models(self) -> List[str]:
        """
        Discover and list all available models from configured LLM providers.

        Returns:
            List of model ID strings in format "provider/model_id"
        """
        self.logger.info("Discovering available models from all providers")

        try:
            models = await get_all_available_models()
            self.logger.info(f"Model discovery completed. Found {len(models)} models")
            return models

        except Exception as e:
            self.logger.error(f"Failed to discover available models: {str(e)}")
            raise

    async def cleanup(self):
        """Clean up resources for all clients."""
        self.logger.info("Cleaning up MultiLLMClient resources")

        try:
            await self.client.cleanup()
            self.logger.info("MultiLLMClient cleanup completed successfully")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
            raise