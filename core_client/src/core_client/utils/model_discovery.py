"""
Model discovery utilities for retrieving available models from LLM providers.
"""

import os
import asyncio
from typing import List

from .logger import get_logger

logger = get_logger(__name__)


async def get_all_available_models() -> List[str]:
    """Get all available model IDs from configured providers."""
    all_models = []

    # Get models from each provider
    tasks = [
        get_openai_models(),
        get_anthropic_models(),
        get_vertex_ai_models()
    ]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    for result in results:
        if isinstance(result, list):
            all_models.extend(result)

    return sorted(all_models)


async def get_openai_models() -> List[str]:
    """Get available OpenAI model IDs."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "your_openai_api_key":
        return []

    try:
        from openai import OpenAI
        client = OpenAI(api_key=api_key)
        response = client.models.list()

        # Filter for relevant models
        models = [
            f"openai/{model.id}" for model in response.data
            if any(keyword in model.id.lower() for keyword in ['gpt', 'o1', 'o3'])
        ]
        return sorted(models)
    except Exception:
        return []


async def get_anthropic_models() -> List[str]:
    """Get available Anthropic model IDs."""
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key or api_key == "your_anthropic_api_key":
        return []

    try:
        from anthropic import Anthropic
        client = Anthropic(api_key=api_key)
        response = client.models.list()

        models = [f"anthropic/{model.id}" for model in response.data]
        return sorted(models)
    except Exception:
        return []


async def get_vertex_ai_models() -> List[str]:
    """Get available Vertex AI model IDs."""
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

    if not project_id or not credentials_path:
        return []

    return [
        "vertex_ai/gemini-2.5-pro-preview-05-06",
        "vertex_ai/gemini-2.5-flash-preview-04-17"
    ]


# Fallback models for when discovery fails
FALLBACK_MODELS = [
    "anthropic/claude-sonnet-4-20250514",
    "anthropic/claude-3-5-sonnet-20241022",
    "openai/gpt-4o",
    "openai/gpt-4o-mini",
    "vertex_ai/gemini-2.5-pro-preview-05-06",
]


def get_model_list_for_ui() -> List[str]:
    """Get model list for UI - returns fallback list in Streamlit context."""
    try:
        asyncio.get_running_loop()
        # In Streamlit context, return fallback
        return FALLBACK_MODELS
    except RuntimeError:
        # Not in event loop, can run async discovery
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            models = loop.run_until_complete(get_all_available_models())
            loop.close()
            return models if models else FALLBACK_MODELS
        except Exception:
            return FALLBACK_MODELS
