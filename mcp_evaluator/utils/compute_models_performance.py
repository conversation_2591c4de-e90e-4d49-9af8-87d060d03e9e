"""
Utility module for computing performance metrics of LLM models based on tool usage.
This module provides functions to evaluate if the tools called by each model match
the expected tools and if the arguments provided are correct.
"""

from typing import Dict, Tuple, List, Any, Optional
import datetime


def compute_models_performance(outputs: List[Dict[str, Any]], models: List[str]) -> Dict[str, Any]:
    """
    Compute performance metrics for each model based on tool usage.
    
    Evaluates if the tools called by each model match the expected tools
    and if the arguments provided are correct.
    
    Args:
        outputs: List of prompt evaluation results from evaluate_all_prompts
        models: List of model names
        
    Returns:
        Dict containing performance metrics for each model
    """
    if not outputs:
        print("No outputs available to compute performance metrics.")
        return None
        
    # Initialize performance metrics
    performance_metrics = {}
    total_prompts = len(outputs)

    for model in models:
        prompts_performance = [compute_prompts_performance_by_model(output, model) for output in outputs]
        right_tools_count = sum(1 for prompt in prompts_performance if prompt["right_tools"])
        right_args_count = sum(1 for prompt in prompts_performance if prompt["right_args"])
        optimal_tools_count = sum(1 for prompt in prompts_performance if prompt["optimal_tools"])
        overall_success_count = sum(1 for prompt in prompts_performance if prompt["right_tools"] and prompt["right_args"])
    
        performance_metrics[model] = {
            "overall": {
                "success_count": overall_success_count,
                "total_prompts": total_prompts,
                "overall_percentage": overall_success_count / total_prompts * 100
            },
            "right_tools": {
                "true": right_tools_count,
                "success_rate": right_tools_count / total_prompts * 100
            },
            "right_args": {
                "true": right_args_count,
                "success_rate": right_args_count / total_prompts * 100
            },
            "optimal_tools": {
                "true": optimal_tools_count,
                "success_rate": right_args_count / total_prompts * 100
            },
            "prompts_performance": prompts_performance
        }
    return performance_metrics


def compute_prompts_performance_by_model(output: List[Dict[str, Any]], model: str) -> Dict[str, Any]:
    right_tools, optimal_tools = check_right_tools_and_optimal(output["expected_tools"], output["model_evaluations"][model]["tool_calls"])
    prompts_performance ={
                    "prompt": output["prompt"],
                    "expected_tools": output["expected_tools"],
                    "actual_tool_calls": output["model_evaluations"][model]["tool_calls"],
                    "right_tools": right_tools,
                    "right_args": check_right_args(output["expected_tools"], output["model_evaluations"][model]["tool_calls"]),
                    "optimal_tools" : optimal_tools
                    }
    return prompts_performance


def check_right_tools_and_optimal(expected_tools: List[Dict[str, Any]], actual_tool_calls: List[Dict[str, Any]]) -> Tuple[bool]:
    """
    Check if the expected tools were used and if the calling is optimal (if the calling uses the minimal number of tools).
    
    Args:
        expected_tools: List of expected tools
        actual_tool_calls: List of actual tool calls
        
    Returns:
        Two booleans indicating if the right tools were used and if it is optimal
    """
    # If no tools were expected, check if no tools were used
    if not expected_tools:
        return len(actual_tool_calls) == 0, len(actual_tool_calls) == 0
    
    
    # Check if all expected tools were used
    expected_tool_names = [tool.get("tool_name") for tool in expected_tools]
    actual_tool_names = [tool.get("tool_name") for tool in actual_tool_calls]
    all_expected_tools_used = all(name in actual_tool_names for name in expected_tool_names)
    optimal_tools = all_expected_tools_used and len(actual_tool_calls) <= len(expected_tools)
    return all_expected_tools_used, optimal_tools


def check_right_args(expected_tools: List[Dict[str, Any]], actual_tool_calls: List[Dict[str, Any]]) -> bool:
    """
    Check if the expected arguments were used for each tool.
    
    Verifies that the expected arguments are included in the actual tool calls,
    without requiring an exact match (additional arguments are allowed).
    
    Args:
        expected_tools: List of expected tools
        actual_tool_calls: List of actual tool calls
        
    Returns: 
        Boolean indicating if the expected arguments were used
    """

    # If no tools were expected, check if no tools were used
    if not expected_tools:
        return True
    
    # Create a dictionary of actual tool calls by name for easier lookup
    res = True
    expected_tools_args_by_name = {tool.get("tool_name"): format_tool_args(tool.get("tool_args")) or None for tool in expected_tools}
    for tool_call in actual_tool_calls:
        tool_name = tool_call.get("tool_name")
        if tool_name in expected_tools_args_by_name.keys() and expected_tools_args_by_name[tool_name]:
            expected_args = expected_tools_args_by_name[tool_name]
            actual_args = tool_call.get("tool_args", {})
            # Check if datetime is on current year
            if "datetime" in actual_args:
                if not actual_args["datetime"].startswith(str(datetime.datetime.now().year)):
                    res = False
            # Check if all expected arguments are present with correct values
            # Only check that one of expected args is included (additional args are allowed)
            if not all(any(token in actual_args[tool_arg_name] for token in tool_arg_expected_tokens) for tool_arg_name, tool_arg_expected_tokens in expected_args.items()):
                res = False
    return res


def format_tool_args(args:Dict[str, str]) -> Dict[str, List[str]]:
    """Format tool args dealing with OR separators

    Args:
        args (Dict[str, str]): Dict of args

    Returns:
        Dict[str, List[str]]: Formatted dict of args
    """
    return {arg : remove_spaces(val.split('OR')) for arg, val in args.items()}


def remove_spaces(strings_list:List[str]) -> List[str]:
    """Remove spaces in strings into a list

    Args:
        strings_list (List[str]): A list of strings

    Returns:
        List[str]: A list of strings without spaces
    """
    return [string.replace(" ", "") for string in strings_list]

