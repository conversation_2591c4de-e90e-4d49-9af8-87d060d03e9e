
"""
Utility module for formatting and processing tool call responses.
This module provides functions to extract, parse, and format tool call results
from various response formats to ensure they are serializable and usable.
"""

import json
import re
from typing import Dict, List, Any, Optional, Union, TypeVar, cast

# Generic type for tool call results
T = TypeVar('T')
ToolCallResult = Union[Dict[str, Any], str, None, T]
ContentType = Union[str, Dict[str, Any], List[Any], Any]
JsonType = Union[Dict[str, Any], List[Any], str, int, float, bool, None]


def format_prompt_result(prompt_data: Dict[str, Any], prompt_result: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    Formats the prompt result to ensure it's serializable and contains all necessary information.
    
    Args:
        prompt_data: The original prompt data
        prompt_result: The raw prompt result from the server
        
    Returns:
        Formatted prompt result
    """
    formatted_result = {
        "prompt": prompt_data.get("prompt", "Prompt missing. Do not respond."),
        "expected_tools": prompt_data.get("expected_tools", []),
        "model_evaluations": {}
    }
    
    for model, response in prompt_result.items():
        if "error" in response:
            formatted_result["model_evaluations"][model] = {
                "success": False,
                "error": response["error"],
                "response": None,
                "tool_calls": []
            }
        else:
            formatted_result["model_evaluations"][model] = {
                "success": True,
                "response": response["response"],
                "tool_calls": format_tool_calls(response.get("tool_calls", []))
            }
    
    return formatted_result



def format_tool_call_result(tool_call_res: Any) -> JsonType:
    """
    Formats a tool call result to make it usable.
    
    Args:
        tool_call_res: The raw tool call result
        
    Returns:
        The formatted result
    """
    # If None, return None
    if tool_call_res is None:
        return None
    
    # If it's a CallToolResult object or similar
    if hasattr(tool_call_res, 'content'):
        # Extract the text from the content
        text_content = extract_text_from_content(tool_call_res.content)
        # Try to parse the text as JSON
        return parse_json_text(text_content)
    
    # If it's a string containing "CallToolResult" or "TextContent"
    if isinstance(tool_call_res, str) and ("CallToolResult" in tool_call_res or "TextContent" in tool_call_res):
        # Extract the text
        text_content = extract_text_from_content(tool_call_res)
        # Try to parse the text as JSON
        return parse_json_text(text_content)
    
    # If it's a dictionary with a 'result' key
    if isinstance(tool_call_res, dict) and 'result' in tool_call_res:
        # Process the result recursively
        return format_tool_call_result(tool_call_res['result'])
    
    # If it's a dictionary with a 'content' key
    if isinstance(tool_call_res, dict) and 'content' in tool_call_res:
        # Extract the text from the content
        text_content = extract_text_from_content(tool_call_res['content'])
        # Try to parse the text as JSON
        return parse_json_text(text_content)
    
    # For other cases, try to parse directly
    if isinstance(tool_call_res, str):
        return parse_json_text(tool_call_res)
    
    # If no special processing is needed, return as is
    return cast(JsonType, tool_call_res)


def format_tool_calls(tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Formats a list of tool calls to ensure they're serializable.
    
    Args:
        tool_calls: List of raw tool calls
        
    Returns:
        Formatted tool calls
    """
    formatted_tool_calls = []
    for tool_call in tool_calls:
        # Extract basic information
        tool_name = tool_call.get("tool_name", "Unknown tool")
        tool_args = tool_call.get("tool_args", {})
        
        # Process the tool call result
        raw_result = tool_call.get("result", None)
        formatted_result = None
        
        if raw_result is not None:
            formatted_result = format_tool_call_result(raw_result)
        
        # Create the formatted entry
        formatted_tool_calls.append({
            "tool_name": tool_name,
            "tool_args": tool_args,
            "tool_result": formatted_result
        })
    
    return formatted_tool_calls


def serialize_tool_call_result(tool_call_res: Any) -> JsonType:
    """
    Serializes a tool call result to ensure it's serializable.
    
    Args:
        tool_call_res: Raw tool call result
        
    Returns:
        Serialized tool call result
    """
    return format_tool_call_result(tool_call_res)



def parse_json_text(text: str) -> JsonType:
    """
    Attempts to parse a text string as JSON.
    
    Args:
        text: The text string to parse
        
    Returns:
        The parsed JSON object or the original text if parsing fails
    """
    if not isinstance(text, str):
        return cast(JsonType, text)
        
    try:
        # Check if the content is valid JSON
        text = text.strip()
        if (text.startswith('{') and text.endswith('}')) or (text.startswith('[') and text.endswith(']')):
            parsed_json = json.loads(text)
            return parsed_json
    except (json.JSONDecodeError, AttributeError):
        # Not valid JSON, return the original text
        pass
        
    return text


def extract_text_from_content(content: ContentType) -> str:
    """
    Extracts text from a content object that can be in various formats.
    
    Args:
        content: The object containing text
        
    Returns:
        The extracted text
    """
    # Case 1: If it's a simple string
    if isinstance(content, str):
        return content
        
    # Case 2: If it's an object with a 'text' attribute
    if hasattr(content, 'text'):
        return cast(str, content.text)
        
    # Case 3: If it's a dictionary with a 'text' key
    if isinstance(content, dict) and 'text' in content:
        return cast(str, content['text'])
        
    # Case 4: If it's a list of objects
    if isinstance(content, list):
        result = []
        for item in content:
            if hasattr(item, 'text'):
                result.append(cast(str, item.text))
            elif isinstance(item, dict) and 'text' in item:
                result.append(cast(str, item['text']))
            else:
                result.append(str(item))
        return '\n'.join(result)
        
    # Case 5: If it's a string representation containing "TextContent"
    if isinstance(content, str) and "TextContent" in content:
        # Try to extract the text using a regular expression
        matches = re.findall(r"text='([^']*)'", content)
        if matches:
            return '\n'.join(matches)
    
    # Default case: convert to string
    return str(content)
