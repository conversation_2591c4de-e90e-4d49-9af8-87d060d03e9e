""" 
Create output directory with the current timestamp into given output folder.
"""

import os
import datetime

from core_client.utils.logger import get_logger



def create_output_directory(output_folder: str, resume_from: str = None) -> str:
    """
    Create output directory with the current timestamp into given output folder.
    
    Args:
        output_folder: Path to the output folder
        resume_from: Optional path to resume from a previous output directory
        
    Returns:
        Path to the created output directory
    """
    # Get a logger for a specific module
    logger = get_logger(__name__)
    if resume_from:
        output_dir = os.path.join(output_folder, f"output_{resume_from}")
    else:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(output_folder, f"output_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Created output directory: {output_dir}")
    return output_dir


