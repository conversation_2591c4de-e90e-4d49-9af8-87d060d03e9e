"""
Utility script to generate performance reports from existing detailed results JSON files.
This allows regenerating reports without re-running the entire evaluation process.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Any

# Add parent directory to path to allow imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.compute_models_performance import compute_models_performance
from utils.load_config import load_config
from reports_generator.reports_generator import generate_html_report


def load_detailed_results(json_path: str) -> List[Dict[str, Any]]:
    """
    Load detailed results from a JSON file.
    
    Args:
        json_path: Path to the detailed results JSON file
        
    Returns:
        List of prompt evaluation results
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        print(f"Loaded {len(results)} prompt results from {json_path}")
        return results
    except Exception as e:
        print(f"Error loading results from {json_path}: {e}")
        sys.exit(1)


def extract_models_from_results(results: List[Dict[str, Any]]) -> List[str]:
    """
    Extract the list of models from the results.
    
    Args:
        results: List of prompt evaluation results
        
    Returns:
        List of model names
    """
    models = set()
    for result in results:
        if "model_evaluations" in result:
            models.update(result["model_evaluations"].keys())
    return list(models)


def generate_report_from_json(
    json_path: str, 
    output_dir: str = None, 
    config_path: str = None
) -> str:
    """
    Generate a performance report from a detailed results JSON file.
    
    Args:
        json_path: Path to the detailed results JSON file
        output_dir: Directory to save the report (defaults to same directory as JSON)
        config_path: Path to the config file (for template directory)
        
    Returns:
        Path to the generated report
    """
    # Load the detailed results
    results = load_detailed_results(json_path)
    
    # Extract models from results
    models = extract_models_from_results(results)
    print(f"Found models: {', '.join(models)}")
    
    # Compute performance metrics
    performance_metrics = compute_models_performance(results, models)
    
    # Determine output directory
    
    if output_dir is None:
        output_dir = os.path.dirname(json_path) 
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate report filename
    json_filename = os.path.basename(json_path)
    report_filename = f"report_from_{json_filename.replace('.json', '.html')}"
    report_path = os.path.join(output_dir, report_filename)
    
    # Default template directory if config not provided
    template_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "reports_generator", 
        "templates"
    )
    
    # Generate the report
    generated_path = generate_html_report(
        performance_metrics=performance_metrics,
        outputs=results,
        output_path=report_path,
        template_dir=template_dir
    )
    
    print(f"Report generated at {generated_path}")
    return generated_path


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate a report from detailed results JSON")
    parser.add_argument("json_path", help="Path to the detailed results JSON file")
    parser.add_argument("--output-dir", help="Directory to save the report (defaults to same directory as JSON)")
    parser.add_argument("--config", help="Path to the config file")
    
    args = parser.parse_args()
    
    generate_report_from_json(
        json_path=args.json_path,
        output_dir=args.output_dir,
        config_path=args.config
    )