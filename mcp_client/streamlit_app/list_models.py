#!/usr/bin/env python3
"""
Simple CLI script to list available LLM models.
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the core_client to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "core_client" / "src"))

from core_client import MultiLLMClient


async def main():
    """List available models."""
    # Load environment variables
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(env_path)
    
    try:
        client = MultiLLMClient()
        models = await client.list_available_models()
        
        print(f"\n🔍 Found {len(models)} available models:")
        print("=" * 50)
        
        for model in models:
            provider = model.split("/")[0].upper()
            print(f"  • {model} ({provider})")
        
        print("=" * 50)
        print(f"Total: {len(models)} models")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
