"""
Utility functions for the Multi-LLM Client Streamlit application.

This module provides a simplified interface to the various utility modules.
"""
import asyncio
from typing import Dict, List, Optional, Any, Coroutine

# Import functions from specialized modules
from .datetime_utils import replace_timedate_placeholder, format_datetime, format_duration
from .formatters import format_journey_data, format_station_schedule_data
from .price_utils import extract_price_info
from .template_utils import get_html_template, get_station_schedule_template, get_transfer_badge
from .train_logos import detect_train_type, get_train_logos


def run_async(coro: Coroutine) -> Any:
    """
    Run an async coroutine safely.

    Args:
        coro: The coroutine to run

    Returns:
        The result of the coroutine or None if an error occurred
    """
    try:
        return asyncio.run(coro)
    except Exception as e:
        print(f"Error running async function: {e}")
        return None


def extract_all_journey_data_from_tool_calls(tool_calls: List[Dict]) -> List[Dict]:
    """
    Extract all journey data from tool calls that have HTML-generating tools.

    Args:
        tool_calls: List of tool calls from the model response

    Returns:
        List of journey data dictionaries
    """
    journey_data_list = []

    for tool_call in tool_calls:
        tool_name = tool_call.get("tool_name", "")

        # Check if this is a tool that generates HTML
        if tool_name in ["get_journeys", "get_departures", "get_arrivals"]:
            result = tool_call.get("result", {})

            # Handle case where result is nested inside another 'result' key (MCP format)
            if isinstance(result, dict) and "result" in result and len(result.keys()) == 1:
                result = result["result"]

                # Handle MCP CallToolResult object
                if hasattr(result, 'content'):
                    content = result.content
                    if hasattr(content, '__iter__') and not isinstance(content, str):
                        # Content is a list, get the first item
                        if len(content) > 0:
                            content_item = content[0]
                            if hasattr(content_item, 'text'):
                                try:
                                    import json
                                    result = json.loads(content_item.text)
                                except json.JSONDecodeError:
                                    result = {}
                            else:
                                result = {}
                        else:
                            result = {}
                    else:
                        result = {}
                elif isinstance(result, str):
                    # Try to parse as JSON if it's a string
                    try:
                        import json
                        result = json.loads(result)
                    except json.JSONDecodeError:
                        result = {}

            if result and isinstance(result, dict):
                # Add tool type to the data for proper formatting
                result["tool_type"] = tool_name
                journey_data_list.append(result)

    return journey_data_list


def extract_journey_data_from_tool_calls(tool_calls: List[Dict]) -> Optional[Dict]:
    """
    Extract first journey data from tool calls.
    Kept for backward compatibility.
    """
    all_data = extract_all_journey_data_from_tool_calls(tool_calls)
    return all_data[0] if all_data else None


def generate_multiple_html_templates(tool_calls: List[Dict], selected_tariff: str = "Tarif Normal") -> str:
    """
    Generate multiple HTML templates for all tool calls that require HTML display.

    Args:
        tool_calls: List of tool calls from the model response
        selected_tariff: The selected tariff type

    Returns:
        Combined HTML string with all templates
    """
    html_sections = []

    # Extract all journey data from tool calls
    journey_data_list = extract_all_journey_data_from_tool_calls(tool_calls)

    print(f"DEBUG: Found {len(journey_data_list)} journey data items to process")

    for i, journey_data in enumerate(journey_data_list):
        tool_type = journey_data.get("tool_type", "")
        print(f"DEBUG: Processing journey data {i+1} with tool_type: {tool_type}")

        # Generate appropriate HTML based on tool type
        if tool_type == "get_journeys":
            print(f"DEBUG: Calling format_journey_data for get_journeys")
            html_content = format_journey_data(journey_data, selected_tariff)
        elif tool_type == "get_departures":
            print(f"DEBUG: Calling format_station_schedule_data for departures")
            html_content = format_station_schedule_data(journey_data, "departures")
        elif tool_type == "get_arrivals":
            print(f"DEBUG: Calling format_station_schedule_data for arrivals")
            html_content = format_station_schedule_data(journey_data, "arrivals")
        else:
            print(f"DEBUG: Unknown tool type: {tool_type}, skipping")
            continue  # Skip unknown tool types

        print(f"DEBUG: Generated HTML content length: {len(html_content) if html_content else 0}")
        print(f"DEBUG: HTML content starts with error: {html_content.startswith('<p>') if html_content else 'No content'}")

        if html_content and not html_content.startswith("<p>"):
            # Add section header for multiple results
            if len(journey_data_list) > 1:
                section_title = f"Résultats de voyage {i + 1}"
                html_sections.append(f'<div style="margin-bottom: 20px;"><h3 style="color: #0088ce; margin-bottom: 10px;">{section_title}</h3>{html_content}</div>')
            else:
                html_sections.append(html_content)
            print(f"DEBUG: Added HTML section {i+1}")
        else:
            print(f"DEBUG: Skipped HTML section {i+1} - invalid content")

    # Combine all HTML sections
    if html_sections:
        combined_html = '\n'.join(html_sections)
        print(f"DEBUG: Generated combined HTML with {len(html_sections)} sections, total length: {len(combined_html)}")
        return combined_html

    print("DEBUG: No HTML sections generated")
    return ""


# Re-export commonly used functions for backward compatibility
__all__ = [
    'replace_timedate_placeholder',
    'format_datetime', 
    'format_duration',
    'format_journey_data',
    'format_station_schedule_data',
    'extract_price_info',
    'get_html_template',
    'get_station_schedule_template',
    'get_transfer_badge',
    'detect_train_type',
    'get_train_logos',
    'generate_multiple_html_templates',
    'extract_all_journey_data_from_tool_calls',
    'extract_journey_data_from_tool_calls',
    'run_async'
]
