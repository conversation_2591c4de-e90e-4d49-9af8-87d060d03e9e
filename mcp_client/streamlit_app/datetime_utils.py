"""
DateTime utilities for SNCF train schedules.

This module handles datetime formatting and placeholder replacement.
"""
import datetime
import re


def replace_timedate_placeholder(text: str) -> str:
    """
    Replace {{TIMEDATE}} and TIMEDATE placeholders with current datetime.

    Args:
        text: The text containing the placeholder

    Returns:
        Text with the placeholder replaced by the current datetime
    """
    if text is None:
        return ""

    current_time = datetime.datetime.now()
    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")

    # Replace both formats of the placeholder
    text = text.replace("{{TIMEDATE}}", formatted_time)
    text = re.sub(r'\bTIMEDATE\b', formatted_time, text)

    return text


def format_datetime(datetime_str: str) -> tuple:
    """
    Format a datetime string from the API into a human-readable format.

    Args:
        datetime_str: Datetime string in format YYYYMMDDThhmmss

    Returns:
        Tuple of (time, date) strings
    """
    if not datetime_str or len(datetime_str) < 15:
        return ("--:--", "")

    try:
        # Extract components using slicing (faster than regex)
        year, month, day = datetime_str[0:4], datetime_str[4:6], datetime_str[6:8]
        hour, minute = datetime_str[9:11], datetime_str[11:13]

        # Format time and date
        return (f"{hour}:{minute}", f"{day}/{month}/{year}")
    except Exception:
        return ("--:--", "")


def format_duration(seconds: int) -> str:
    """
    Format duration in seconds to hours and minutes.

    Args:
        seconds: Duration in seconds

    Returns:
        Formatted duration string
    """
    if not seconds:
        return "--:--"

    hours, minutes = divmod(seconds, 3600)
    minutes //= 60

    return f"{hours}h {minutes:02d}min" if hours > 0 else f"{minutes}min"
