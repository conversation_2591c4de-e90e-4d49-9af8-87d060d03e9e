"""
Main Streamlit application for TravelBot IA.

This application provides a user interface for interacting with LLM models
using the MCP protocol for tool calling, focused on SNCF train information.
"""
import os
import sys
import streamlit as st
import nest_asyncio
import asyncio

# Add the parent directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Load environment variables from core_client/.env file
from dotenv import load_dotenv
load_dotenv()

# Apply nest_asyncio to allow nested event loops (needed for Streamlit)
nest_asyncio.apply()

# Import application modules
from streamlit_app.session_state import initialize_session_state, add_user_message, reset_new_message_flag
from streamlit_app.client_manager import get_client, initialize_client, reset_client, get_loop
from streamlit_app.ui_components import display_conversation_history, display_sidebar
from streamlit_app.utils import replace_timedate_placeholder
from streamlit_app.query_processor import process_query

# Check if API keys are set
def check_api_keys():
    """Check if the necessary API keys are set in the environment variables."""
    missing_keys = []

    # Check for OpenAI API key
    if not os.environ.get("OPENAI_API_KEY") or os.environ.get("OPENAI_API_KEY") == "your_openai_api_key":
        missing_keys.append("OPENAI_API_KEY")

    # Check for Anthropic API key
    if not os.environ.get("ANTHROPIC_API_KEY") or os.environ.get("ANTHROPIC_API_KEY") == "your_anthropic_api_key":
        missing_keys.append("ANTHROPIC_API_KEY")

    return missing_keys

# Main application function
def main():
    """Main function to run the Streamlit application."""
    # Set page configuration
    st.set_page_config(
        page_title="TravelBot IA",
        page_icon="🚄",
        layout="wide",
    )

    # Initialize session state
    initialize_session_state()

    # Check for missing API keys
    missing_keys = check_api_keys()
    if missing_keys:
        st.warning(f"⚠️ The following API keys are missing or not properly set: {', '.join(missing_keys)}")
        st.info("""
        To use all models, please set up your API keys in the `.env` file in the `core_client/src/core_client` directory.

        Example:
        ```
        OPENAI_API_KEY=your_openai_api_key
        ANTHROPIC_API_KEY=your_anthropic_api_key
        ```

        You can still use the application with the models that have valid API keys.
        """)

    # Get models, system prompt, and selected tariff from sidebar
    selected_models, system_prompt_text, selected_tariff = display_sidebar()

    # Store selected tariff in session state
    st.session_state.selected_tariff = selected_tariff

    # Store current system prompt for next comparison
    st.session_state.last_system_prompt = system_prompt_text

    # Main chat interface
    st.title("TravelBot IA")

    # Display conversation history
    display_conversation_history(st.session_state.messages)

    # Check if we need to process a new message
    if "new_message" in st.session_state and st.session_state.new_message:
        prompt = st.session_state.current_prompt

        if not selected_models:
            with st.chat_message("assistant"):
                st.error("Veuillez sélectionner au moins un modèle dans la barre latérale.")
                # Reset the new message flag
                reset_new_message_flag()
        else:
            # Initialize client if needed
            if get_client() is None:
                with st.spinner("Initialisation du client..."):
                    # Apply the system prompt with the current date/time before initializing
                    formatted_prompt = replace_timedate_placeholder(system_prompt_text)
                    if initialize_client(selected_models, formatted_prompt):
                        # Don't show success message to avoid cluttering the interface
                        pass
                    else:
                        st.error("Échec de l'initialisation du client. Veuillez réessayer.")
                        # Reset the new message flag
                        reset_new_message_flag()
                        st.stop()
            # Check if models have changed since last query
            elif "last_models" in st.session_state and set(st.session_state.last_models) != set(selected_models):
                with st.spinner("Modèles modifiés, mise à jour du client..."):
                    # Get the current client and update its models
                    client = get_client()
                    loop = get_loop()

                    # We need to reset and create a new client to change models
                    reset_client()
                    # Apply the system prompt with the current date/time before initializing
                    formatted_prompt = replace_timedate_placeholder(system_prompt_text)
                    if initialize_client(selected_models, formatted_prompt):
                        # Don't show success message to avoid cluttering the interface
                        pass
                    else:
                        st.error("Échec de la mise à jour du client. Veuillez réessayer.")
                        # Reset the new message flag
                        reset_new_message_flag()
                        st.stop()
            # Update system prompt if it has changed
            elif system_prompt_text != st.session_state.get("last_system_prompt", ""):
                with st.spinner("Mise à jour du message système..."):
                    client = get_client()
                    loop = get_loop()
                    if client and loop:
                        # Apply the system prompt with the current date/time
                        formatted_prompt = replace_timedate_placeholder(system_prompt_text)
                        future = asyncio.run_coroutine_threadsafe(
                            client.update_system_prompt(formatted_prompt),
                            loop
                        )
                        future.result(timeout=5)
                        # Store the updated system prompt
                        st.session_state.last_system_prompt = system_prompt_text

            # Store current models for next comparison
            st.session_state.last_models = selected_models.copy()

            # Show a spinner at the bottom of the page instead of in a new chat message
            with st.spinner("Traitement de votre demande..."):
                # Process the query without creating a new chat message container
                process_query(prompt)

            # Reset the new message flag
            reset_new_message_flag()

    # User input
    if prompt := st.chat_input("Que voulez-vous savoir sur les trains SNCF ?"):
        # Add user message to chat history
        add_user_message(prompt)

        # Rerun to display the user message and process it
        st.rerun()

# Run the application if this script is executed directly
if __name__ == "__main__":
    main()
