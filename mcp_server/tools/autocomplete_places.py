"""
Tool for autocompleting place names.
"""

from typing import Any, List, Dict
from mcp.types import Tool

from tools import SncfTools

from utils.sncf_api import make_sncf_api_request

async def autocomplete_places(
    query: str,
    searched_type: str = "stop_area",
    count: int = 10
) -> Any:
    """
    Search and find exact station codes and place identifiers for French transportation locations.

    Args:
        query: Search query string exactly as provided by user
        searched_type: Type of places to search for (stop_area, stop_point, administrative_region, address, poi)
        count: Maximum number of results to return (1-50)

    Returns:
        List of places with id (station codes) and name (human-readable names)
    """
    suggestions = await make_sncf_api_request(f"places?q={query}&type[]={searched_type}&count={count}&data_freshness=realtime")

    if suggestions is None:
        return "Error: No response from SNCF API"
    elif "error" in suggestions:
        return f"Error: {suggestions['error']['message']}"

    if "places" not in suggestions or not suggestions["places"]:
        return "Error: No suggestions found"

    # Filter results to only include id and name
    filtered_places = [{"id": place.get("id"), "name": place.get("name")} for place in suggestions["places"]]

    return filtered_places


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the autocomplete_places tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.AUTOCOMPLETE_PLACES.value,
        description="""Search and find exact station codes and place identifiers for French transportation locations.

🔍 **PRIMARY USE CASES**:
• Find station codes when location is not recognized by get_journeys
• Discover exact station names in cities with multiple stations
• Get precise identifiers for ambiguous location names
• Resolve unclear or misspelled place names

⚠️ **WHEN TO USE THIS TOOL**:
• **ONLY when get_journeys fails** to recognize a location
• **ONLY when you need specific station codes** (stop_area:, stop_point:, admin:fr:)
• **NOT for common cities** like Paris, Lyon, Marseille (use city names directly in get_journeys)
• **NOT when user provides clear station names** (use them directly in get_journeys)

🎯 **SEARCH STRATEGY**:
• Use the exact user input as query (don't modify or translate)
• Start with 'stop_area' for train stations (most common)
• Use 'administrative_region' for cities/regions
• Results provide both human-readable names and technical codes

📊 **OUTPUT**: Returns list of places with station codes and names for use in other tools.""",
        inputSchema={
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "🔍 Search term exactly as provided by user. Examples: 'Gare du Nord', 'Aéroport Charles de Gaulle', 'Lille Europe'. Do NOT modify, translate, or interpret the user's input.",
                },
                "searched_type": {
                    "type": "string",
                    "enum": ["stop_area", "stop_point", "administrative_region", "address", "poi"],
                    "description": "🎯 Type of location to search for: 'stop_area' (train stations - most common), 'stop_point' (specific platforms), 'administrative_region' (cities/regions), 'address' (street addresses), 'poi' (points of interest).",
                    "default": "stop_area"
                },
                "count": {
                    "type": "integer",
                    "description": "📊 Maximum number of results to return (1-15).",
                    "default": 10
                }
            },
            "required": ["query"]
        }
    )
