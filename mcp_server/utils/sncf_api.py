"""
Utility functions for making requests to the SNCF API.
"""

import httpx
import os
from utils.logger import get_logger

# Get logger for this module
logger = get_logger(__name__)

# Get API key from environment
SNCF_API_KEY = os.getenv("SNCF_API_KEY")
SNCF_BASE_URL = os.getenv("SNCF_BASE_URL", "https://api.sncf.com/v1")

# Verify API key is set
if not SNCF_API_KEY:
    logger.critical("SNCF_API_KEY environment variable is not set")
    raise ValueError("SNCF_API_KEY is not set")

logger.debug(f"SNCF API configured with base URL: {SNCF_BASE_URL}")

async def make_sncf_api_request(url: str) -> dict:
    """
    Make a request to the SNCF API with proper error handling.

    Args:
        url: The API endpoint path to request

    Returns:
        JSON response as dictionary or None if the request failed
    """
    headers = {"Authorization": SNCF_API_KEY}
    full_url = f"{SNCF_BASE_URL}/coverage/sncf{url}"
    logger.debug(f"Making SNCF API request to: {full_url}")

    async with httpx.AsyncClient(base_url=f"{SNCF_BASE_URL}/coverage/sncf") as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            logger.debug(f"SNCF API request successful: {response.status_code}")
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"SNCF API error: {e.response.status_code} - {e.response.text}")
            return e.response.json() if e.response else None
        except Exception as e:
            logger.error(f"SNCF request error: {str(e)}", exc_info=True)
            return None