"""
Utility functions for formatting commercial modes for SNCF API requests.
"""

def format_commercial_modes(commercial_modes: list[str] | None) -> list[str] | None:
    """
    Format commercial modes for API request.

    Args:
        commercial_modes: List of commercial mode names (e.g., ["<PERSON><PERSON>G<PERSON>", "INO<PERSON>", ...])

    Returns:
        List of formatted commercial mode identifiers for the API, or None if input is None
    """
    if commercial_modes is None:
        return None

    formatted_modes = []

    for mode in commercial_modes:
        formatted_mode = mode.upper()

        # Map commercial mode names to API identifiers
        if formatted_mode == "OUIGO":
            formatted_mode = "TGVOUIGO"
        elif formatted_mode == "INOUI":
            formatted_mode = "OUI"
        elif formatted_mode == "INTERCITE":
            formatted_mode = "IC"
        elif formatted_mode == "INTERCITE DE NUIT":
            formatted_mode = "ICN"
        elif formatted_mode == "TER":
            formatted_mode = "TER"
        elif formatted_mode == "OUIGO TRAIN CLASSIQUE":
            formatted_mode = "OUIGO_TC"

        # Add the commercial_mode prefix
        formatted_modes.append(f"commercial_mode:{formatted_mode}")

    return formatted_modes