"""
Utility functions for making requests to the SNCF OpenData API.
"""

import httpx
import os
from typing import Optional
from utils.logger import get_logger

# Get logger for this module
logger = get_logger(__name__)

# Get API key from environment
SNCF_OPENDATA_API_KEY = os.getenv("SNCF_OPENDATA_API_KEY")
SNCF_OPENDATA_BASE_URL = os.getenv("SNCF_OPENDATA_BASE_URL")

# Verify API key is set
if not SNCF_OPENDATA_API_KEY:
    logger.critical("SNCF_OPENDATA_API_KEY environment variable is not set")
    raise ValueError("SNCF_OPENDATA_API_KEY is not set")

logger.debug(f"SNCF OpenData API configured with base URL: {SNCF_OPENDATA_BASE_URL}")

async def make_sncf_opendata_request(url: str) -> dict:
    """
    Make a request to the SNCF Opendata API with proper error handling.

    Args:
        url: The full URL to request

    Returns:
        JSON response as dictionary or None if the request failed
    """
    headers = {"Authorization": SNCF_OPENDATA_API_KEY}
    logger.debug(f"Making SNCF OpenData API request to: {url}")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            logger.debug(f"SNCF OpenData API request successful: {response.status_code}")
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"SNCF OpenData API error: {e.response.status_code} - {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"SNCF OpenData request error: {str(e)}", exc_info=True)
            return None


async def get_prices_from_opendata(departure: str, arrival: str, classe: int) -> Optional[str]:
    """
    Get minimal and maximal prices from SNCF Opendata API for a given departure and arrival.

    Args:
        departure: Departure station code
        arrival: Arrival station code
        classe: Travel class (1 or 2)

    Returns:
        Formatted string with price information or None if no prices found
    """
    logger.debug(f"Prices get_prices_from_opendata Args: {departure}, {arrival}, {classe}")
    dataset_name = "tarifs-tgv-inoui-ouigo"
    departure_station_code_uic = str(departure).split(":")[-1]
    arrival_station_code_uic = str(arrival).split(":")[-1]

    request = f"{SNCF_OPENDATA_BASE_URL}/catalog/datasets/{dataset_name}/records?where=gare_origine_code_uic%3D{departure_station_code_uic}%20and%20gare_destination_code_uic%3D{arrival_station_code_uic}%20and%20classe%3D{classe}&limit=20"
    response = await make_sncf_opendata_request(request)

    logger.debug(f"Prices get_prices_from_opendata Response: {response}")
    if response is None:
        return None

    if "results" not in response or len(response["results"]) == 0:
        return None

    # Format prices in json
    """
    {
        normal: "10 - 100",
        tarif_reduit: "5 - 50"
    }
    """
    prices = {}
    for price in response["results"]:
        profil_tarifaire = price.get("profil_tarifaire", "")
        prix_minimum = price.get("prix_minimum", 0.0)
        prix_maximum = price.get("prix_maximum", 0.0)
        prices[profil_tarifaire] = f"{prix_minimum} - {prix_maximum}"

    return prices